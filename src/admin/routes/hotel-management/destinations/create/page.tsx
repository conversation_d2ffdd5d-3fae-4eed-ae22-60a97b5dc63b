import { FocusModal, Toaster, toast } from "@camped-ai/ui";
import DestinationFormModern, { DestinationFormData } from "../../../../components/destination-form-modern";
import { useNavigate } from "react-router-dom";
import { useState } from "react";

export const DestinationCreate = () => {
  const navigate = useNavigate();
  const open = true;

  // Define the initial form data as a constant to ensure consistency
  const initialFormData: DestinationFormData = {
    name: "",
    handle: "",
    description: "",
    is_active: true,
    is_featured: false,
    country: "",
    location: null,
    tags: null,
    website: null,
    media: [],
    faqs: [],
  };

  const [formData, setFormData] = useState<DestinationFormData>(initialFormData);

  const handleModalClose = () => {
    navigate("/hotel-management/destinations");
  };

  const handleCreate = async (updatedData?: DestinationFormData) => {
    const dataToUse = updatedData || formData;

    console.log("🔍 handleCreate received data:", {
      hasUpdatedData: !!updatedData,
      mediaCount: dataToUse.media?.length || 0,
      mediaDetails:
        dataToUse.media?.map((m) => ({
          hasFile: !!m?.file,
          fileName: m?.file?.name,
          fileSize: m?.file?.size,
          fileType: m?.file?.type,
          isThumbnail: m?.isThumbnail,
        })) || [],
      faqCount: dataToUse.faqs?.length || 0,
    });

    try {
      let formattedTags = dataToUse.tags;
      if (typeof dataToUse.tags === "string") {
        try {
          formattedTags = JSON.parse(dataToUse.tags as string);
        } catch (e) {
          formattedTags = (dataToUse.tags as string)
            .split(",")
            .map((tag) => tag.trim());
        }
      }

      const destinationData = {
        name: dataToUse.name,
        handle: dataToUse.handle,
        description: dataToUse.description,
        is_active: dataToUse.is_active,
        is_featured: dataToUse.is_featured,
        country: dataToUse.country,
        location: dataToUse.location,
        tags: formattedTags,
        website: dataToUse.website,
        faqs: dataToUse.faqs || [],
      };

      if (destinationData.faqs && destinationData.faqs.length > 0) {
        const invalidFaqs = destinationData.faqs.filter(
          (faq) => !faq.question?.trim() || !faq.answer?.trim()
        );

        if (invalidFaqs.length > 0) {
          toast.error("Invalid FAQ Data", {
            description:
              "All FAQ entries must have both question and answer filled out.",
          });
          return false;
        }
      }

      console.log("🔍 Destination creation payload:", {
        name: destinationData.name,
        faqCount: destinationData.faqs?.length || 0,
        faqs: destinationData.faqs,
        hasMedia: (dataToUse.media?.length || 0) > 0,
        mediaCount: dataToUse.media?.length || 0,
      });

      const response = await fetch("/admin/hotel-management/destinations", {
        method: "POST",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(destinationData),
      });

      const data = await response.json();

      if (response.ok && data.destination) {
        const destinationId = data.destination.id;

        console.log("✅ Destination created successfully:", {
          id: destinationId,
          name: data.destination.name,
          faqsCreated: data.destination.faqs?.length || 0,
        });

        if (dataToUse.media && dataToUse.media.length > 0) {
          console.log(`📸 Uploading ${dataToUse.media.length} images...`);

          const validImages = dataToUse.media.filter((media) => {
            console.log("🔍 Validating media item:", {
              hasFile: !!media.file,
              fileName: media.file?.name,
              fileSize: media.file?.size,
              fileType: media.file?.type,
              url: media.url?.substring(0, 50) + "...",
            });

            if (!media.file) {
              console.log("❌ Media item rejected: No file object");
              return false;
            }

            const maxSize = 10 * 1024 * 1024; // 10MB
            if (media.file.size > maxSize) {
              toast.error("Image Too Large", {
                description: `"${media.file.name}" is too large. Maximum size is 10MB.`,
              });
              return false;
            }

            const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"];
            if (!allowedTypes.includes(media.file.type)) {
              toast.error("Invalid Image Type", {
                description: `"${media.file.name}" is not a supported image format. Use JPEG, PNG, or WebP.`,
              });
              return false;
            }

            console.log("✅ Media item passed validation");
            return true;
          });

          console.log(
            `🔍 Validation results: ${validImages.length}/${dataToUse.media.length} images passed validation`
          );

          if (validImages.length !== dataToUse.media.length) {
            console.log(
              `⚠️ ${dataToUse.media.length - validImages.length} images failed validation`
            );
          }

          let uploadedCount = 0;
          let failedCount = 0;

          for (const media of validImages) {
            if (media.file) {
              console.log(`🔍 Preparing upload for: ${media.file.name}`);

              const formData = new FormData();
              formData.append("files", media.file);

              const metadata = {
                isThumbnail: media.isThumbnail,
              };
              formData.append("metadata", JSON.stringify(metadata));

              console.log(`🔍 FormData prepared for ${media.file.name}:`, {
                fileSize: media.file.size,
                fileType: media.file.type,
                metadata: metadata,
                destinationId: destinationId,
              });

              try {
                const uploadResponse = await fetch(
                  `/admin/hotel-management/destinations/${destinationId}/upload`,
                  {
                    method: "POST",
                    credentials: "include",
                    body: formData,
                  }
                );

                console.log(`🔍 Upload response for ${media.file.name}:`, {
                  status: uploadResponse.status,
                  statusText: uploadResponse.statusText,
                  ok: uploadResponse.ok,
                });

                if (!uploadResponse.ok) {
                  const errorText = await uploadResponse.text();
                  console.error(
                    `❌ Failed to upload image "${media.file.name}":`,
                    {
                      status: uploadResponse.status,
                      statusText: uploadResponse.statusText,
                      errorText: errorText,
                    }
                  );
                  failedCount++;

                  toast.error("Image Upload Failed", {
                    description: `Failed to upload "${media.file.name}". Please try again.`,
                  });
                } else {
                  const responseData = await uploadResponse.json();
                  uploadedCount++;
                  console.log(
                    `✅ Successfully uploaded image "${media.file.name}":`,
                    responseData
                  );
                }
              } catch (uploadError) {
                console.error(
                  `❌ Error uploading image "${media.file.name}":`,
                  uploadError
                );
                failedCount++;

                toast.error("Upload Error", {
                  description: `Network error while uploading "${media.file.name}". Please check your connection.`,
                });
              }
            }
          }

          console.log(
            `📸 Image upload summary: ${uploadedCount} successful, ${failedCount} failed`
          );

          if (uploadedCount > 0 && failedCount > 0) {
            toast.warning("Partial Upload Success", {
              description: `${uploadedCount} images uploaded successfully, ${failedCount} failed.`,
            });
          } else if (uploadedCount > 0 && failedCount === 0) {
            console.log("✅ All images uploaded successfully");
            toast.success("Images Uploaded", {
              description: `Successfully uploaded ${uploadedCount} image${uploadedCount > 1 ? "s" : ""}.`,
            });
          } else if (validImages.length > 0 && uploadedCount === 0) {
            toast.error("Upload Failed", {
              description: "All image uploads failed. Please try again.",
            });
          }
        }

        console.log("Checking for translation data:", {
          hasTranslationData: !!dataToUse.translationData,
          destinationId,
        });
        if (dataToUse.translationData) {
          console.log(
            "Saving translations for new destination:",
            destinationId,
            dataToUse.translationData
          );
          try {
            await dataToUse.translationData.saveFunction(destinationId);
            console.log("Translations saved successfully for new destination");
          } catch (translationError) {
            console.error(
              "Error saving translations for new destination:",
              translationError
            );
          }
        } else {
          console.log("No translation data to save for new destination");
        }

        toast.success("Success", {
          description: "Destination created successfully",
        });

        setFormData({ ...initialFormData });

        navigate("/hotel-management/destinations");
        return true;
      } else {
        console.error("❌ Destination creation failed:", {
          status: response.status,
          statusText: response.statusText,
          data: data,
          faqCount: destinationData.faqs?.length || 0,
          hasImages: (dataToUse.media?.length || 0) > 0,
        });

        let errorMessage = "Failed to create destination";
        if (data.message) {
          errorMessage = data.message;
        } else if (response.status === 400) {
          errorMessage =
            "Invalid data provided. Please check all fields and try again.";
        } else if (response.status === 403) {
          errorMessage = "You don't have permission to create destinations.";
        } else if (response.status === 409) {
          errorMessage = "A destination with this handle already exists.";
        } else if (response.status >= 500) {
          errorMessage = "Server error occurred. Please try again later.";
        }

        if (data.errors && Array.isArray(data.errors)) {
          const faqErrors = data.errors.filter(
            (err: any) => err.path && err.path.includes("faqs")
          );
          if (faqErrors.length > 0) {
            errorMessage += " Please check your FAQ entries.";
          }
        }

        toast.error("Creation Failed", {
          description: errorMessage,
        });
        return false;
      }
    } catch (error) {
      console.error("❌ Unexpected error creating destination:", error);

      let errorMessage = "An unexpected error occurred";
      if (error instanceof TypeError && (error as any).message?.includes("fetch")) {
        errorMessage =
          "Network error. Please check your connection and try again.";
      } else if (error instanceof SyntaxError) {
        errorMessage = "Invalid response from server. Please try again.";
      }

      toast.error("Error", {
        description: errorMessage,
      });
      return false;
    }
  };

  return (
    <>
      <Toaster />
      <FocusModal open={open} onOpenChange={handleModalClose}>
        <FocusModal.Content className="w-full h-full destination-modal">
          <DestinationFormModern
            key={open ? "destination-form-open" : "destination-form-closed"}
            formData={formData}
            setFormData={setFormData}
            onSubmit={handleCreate}
            closeModal={() => handleModalClose()}
          />
        </FocusModal.Content>
      </FocusModal>
    </>
  );
};
