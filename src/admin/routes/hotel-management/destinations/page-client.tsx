import { ChevronLeft, ChevronRight } from "lucide-react";
import {
  StatusFilterBadges,
  StatusBadges,
} from "../../../components/shared/StatusFilterBadges";

import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Toaster,
  Select,
} from "@camped-ai/ui";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

import DestinationFormModern, {
  DestinationFormData,
} from "../../../components/destination-form-modern";
import { useDestinationsManagement } from "../../../hooks/hotel-management/use-destinations-management";
import DestinationSkeleton from "../../../components/destination/destination-skeleton";
import "../../../styles/destination-modal-fix.css";
import "../../../styles/destination-badges.css";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useRbac } from "../../../hooks/use-rbac";

const PageClient = () => {
  const navigate = useNavigate();
  const { hasPermission } = useRbac();
  const { t } = useTranslation();

  // Use the new destinations management hook
  const {
    destinations,
    isLoading,
    isFetching,
    filters,
    pagination,
    nextPage,
    previousPage,
    goToPage,
    changePageSize,
    updateSearch,
    updateStatusFilter,
  } = useDestinationsManagement();

  // Define the initial form data as a constant to ensure consistency
  const initialFormData: DestinationFormData = {
    name: "",
    handle: "",
    description: "",
    is_active: true,
    is_featured: false,
    country: "",
    location: null,
    tags: null,
    website: null,
    media: [],
    faqs: [],
  };
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [showFilters, setShowFilters] = useState(false);
  const [formData, setFormData] =
    useState<DestinationFormData>(initialFormData);

  // Since filtering is now handled by the backend API, we don't need client-side filtering
  // The destinations from the hook are already filtered and paginated

  // Get a gradient color based on index
  const getGradient = (index: number) => {
    const gradients = [
      "from-blue-500 to-purple-500",
      "from-green-500 to-teal-500",
      "from-yellow-500 to-orange-500",
      "from-pink-500 to-rose-500",
      "from-indigo-500 to-blue-500",
      "from-red-500 to-pink-500",
    ];
    return gradients[index % gradients.length];
  };

  const path = "/hotel-management/destinations";
  const hasCreate = hasPermission("destinations:create");
  const hasImport = hasPermission("destinations:bulk_import");
  const hasExport = hasPermission("destinations:export");

  return (
    <>
      <Container className="divide-y p-0">
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4">
          <div>
            <Heading level="h2">Destination</Heading>
          </div>
          <div className="flex items-center gap-x-2">
            {hasExport && (
              <Button
                variant="secondary"
                size="small"
                onClick={() => navigate(`${path}/export`)}
                className="flex items-center gap-2"
              >
                Export
              </Button>
            )}
            {hasImport && (
              <Button
                variant="secondary"
                size="small"
                onClick={() => navigate(`${path}/import`)}
                className="flex items-center gap-2"
              >
                Import
              </Button>
            )}
            {hasCreate && (
              <Button size="small" asChild>
                <Link to={`${path}/create`}>{t("actions.create")}</Link>
              </Button>
            )}
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-grow">
            <Input
              placeholder="Search destinations..."
              value={filters.search || ""}
              onChange={(e) => updateSearch(e.target.value)}
              className="h-9 bg-background border-border text-foreground placeholder:text-muted-foreground focus:ring-ring focus:border-ring pr-10"
            />
            {isFetching && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <div className="animate-spin h-4 w-4 border-2 border-ui-border-base border-t-ui-fg-base rounded-full"></div>
              </div>
            )}
          </div>
          <Button
            variant="secondary"
            size="small"
            onClick={() => setShowFilters(!showFilters)}
            className="whitespace-nowrap bg-background border border-border shadow-sm hover:bg-muted flex items-center gap-2 px-3 py-2 rounded-md transition-all"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
              />
            </svg>
            <span>{showFilters ? "Hide Filters" : "Show Filters"}</span>
          </Button>

          <div className="flex gap-2">
            <div className="flex gap-2">
              <Button
                variant={viewMode === "grid" ? "primary" : "secondary"}
                size="small"
                onClick={() => setViewMode("grid")}
                // className={`px-3 py-2 rounded-md shadow-sm flex items-center justify-center ${
                //   viewMode === "grid"
                //     ? "bg-primary text-primary-foreground"
                //     : "bg-background border border-border hover:bg-accent text-foreground"
                // }`}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="w-5 h-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                </svg>
              </Button>

              <Button
                variant={viewMode === "list" ? "primary" : "secondary"}
                size="small"
                onClick={() => setViewMode("list")}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="w-5 h-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
              </Button>
            </div>
          </div>
        </div>

        {/* Collapsible Filter Section */}
        {showFilters && (
          <StatusFilterBadges
            filters={filters}
            updateStatusFilter={updateStatusFilter}
          />
        )}

        {isLoading || isFetching ? (
          <DestinationSkeleton
            viewMode={viewMode}
            count={viewMode === "grid" ? 6 : 10}
          />
        ) : destinations.length > 0 ? (
          viewMode === "grid" ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {destinations.map((destination, index) => (
                <div
                  key={destination.id}
                  className="border border-border rounded-lg overflow-hidden bg-card shadow-sm hover:shadow-md transition-all cursor-pointer flex flex-col"
                  onClick={() =>
                    navigate(
                      `/hotel-management/destinations/${destination.handle}`
                    )
                  }
                >
                  <div className="h-40 relative flex-shrink-0">
                    {destination.thumbnailUrl ? (
                      <>
                        <img
                          src={destination.thumbnailUrl}
                          alt={destination.name}
                          className="w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                          <Heading
                            level="h3"
                            className="text-white text-2xl font-bold"
                          >
                            {destination.name}
                          </Heading>
                        </div>
                      </>
                    ) : (
                      <div
                        className={`h-full w-full bg-gradient-to-r ${getGradient(
                          index
                        )} flex items-center justify-center`}
                      >
                        <Heading
                          level="h3"
                          className="text-white text-2xl font-bold"
                        >
                          {destination.name}
                        </Heading>
                      </div>
                    )}
                    {/* Hotel count badge */}
                    <div className="absolute top-2 right-2 bg-white/45 rounded-full px-2 py-1 text-xs font-medium flex items-center gap-1 shadow-sm">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="w-3.5 h-3.5 text-blue-600"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
                      </svg>
                      <span className="text-foreground">
                        {destination.hotelCount || 0} Hotels
                      </span>
                    </div>
                  </div>
                  <div className="p-4 flex flex-col flex-grow">
                    <div className="flex-grow flex flex-col">
                      <div className="flex justify-between items-center mb-2">
                        <Text className="font-medium">
                          {destination.country}
                        </Text>
                        <StatusBadges
                          isActive={destination.is_active}
                          isFeatured={destination.is_featured}
                          size="small"
                        />
                      </div>
                      <Text className="text-sm text-muted-foreground line-clamp-2 mb-3">
                        {destination.description || "No description available"}
                      </Text>
                    </div>

                    {/* Action buttons */}
                    <div className="flex gap-2 mt-auto">
                      <Button
                        variant="secondary"
                        size="small"
                        className="bg-background border border-border shadow-sm hover:bg-accent flex items-center gap-1 px-3 py-1.5 rounded-md transition-all text-foreground text-xs flex-1"
                        onClick={(e) => {
                          e.stopPropagation();
                          navigate(
                            `/hotel-management/destinations/${destination.handle}`
                          );
                        }}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="w-3.5 h-3.5"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                          <path
                            fillRule="evenodd"
                            d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                        <span>View</span>
                      </Button>
                      <Button
                        variant="secondary"
                        size="small"
                        className="bg-background border border-border shadow-sm hover:bg-accent flex items-center gap-1 px-3 py-1.5 rounded-md transition-all text-foreground text-xs flex-1"
                        onClick={(e) => {
                          e.stopPropagation();
                          // Navigate to hotels filtered by this destination
                          navigate(
                            `/hotel-management/hotels?destination=${destination.id}`
                          );
                        }}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="w-3.5 h-3.5"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
                        </svg>
                        <span>Hotels</span>
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="border border-border rounded-lg overflow-hidden bg-card shadow-sm">
              <table className="min-w-full divide-y divide-border">
                <thead className="bg-muted/50">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"
                    >
                      Destination
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"
                    >
                      Country
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"
                    >
                      Hotels
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"
                    >
                      Status
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"
                    >
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-card divide-y divide-border">
                  {destinations.map((destination) => (
                    <tr
                      key={destination.id}
                      className="hover:bg-accent cursor-pointer"
                      onClick={() =>
                        navigate(
                          `/hotel-management/destinations/${destination.handle}`
                        )
                      }
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10 relative">
                            {destination.thumbnailUrl ? (
                              <img
                                className="h-10 w-10 rounded-md object-cover"
                                src={destination.thumbnailUrl}
                                alt=""
                              />
                            ) : (
                              <div className="h-10 w-10 rounded-md bg-gradient-to-r from-blue-500 to-purple-500"></div>
                            )}
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-foreground">
                              {destination.name}
                            </div>
                            <div className="text-sm text-muted-foreground truncate max-w-xs">
                              {destination.description || "No description"}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-foreground">
                          {destination.country || "--"}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-foreground font-medium">
                          {destination.hotelCount || 0}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <StatusBadges
                          isActive={destination.is_active}
                          isFeatured={destination.is_featured}
                          size="small"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex gap-2">
                          <Button
                            variant="secondary"
                            size="small"
                            className="bg-background border border-border shadow-sm hover:bg-accent flex items-center gap-1 px-3 py-1.5 rounded-md transition-all text-foreground text-xs"
                            onClick={(e) => {
                              e.stopPropagation();
                              navigate(
                                `/hotel-management/destinations/${destination.handle}`
                              );
                            }}
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="w-3.5 h-3.5"
                              viewBox="0 0 20 20"
                              fill="currentColor"
                            >
                              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                              <path
                                fillRule="evenodd"
                                d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                                clipRule="evenodd"
                              />
                            </svg>
                            <span>View</span>
                          </Button>
                          <Button
                            variant="secondary"
                            size="small"
                            className="bg-background border border-border shadow-sm hover:bg-accent flex items-center gap-1 px-3 py-1.5 rounded-md transition-all text-foreground text-xs"
                            onClick={(e) => {
                              e.stopPropagation();
                              // Navigate to hotels filtered by this destination
                              navigate(
                                `/hotel-management/hotels?destination=${destination.id}`
                              );
                            }}
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="w-3.5 h-3.5"
                              viewBox="0 0 20 20"
                              fill="currentColor"
                            >
                              <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
                            </svg>
                            <span>Hotels</span>
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )
        ) : (
          <div className="text-center py-12 bg-muted/50 rounded-lg">
            <Text className="text-muted-foreground">
              {filters.search ||
              filters.is_featured !== null ||
              filters.is_active !== null
                ? "No destinations match your search criteria"
                : "No destinations found"}
            </Text>
          </div>
        )}

        {/* Pagination controls */}
        {destinations.length > 0 && (
          <div className="flex items-center justify-between p-4 border-t border-ui-border-base bg-ui-bg-base">
            {/* Total count on the left */}
            <div className="flex items-center">
              <Text className="text-sm text-ui-fg-base font-medium">
                Total Arrivals: {pagination.totalCount}
              </Text>
            </div>

            {/* Page numbers in the center */}
            <div className="flex items-center gap-1">
              {/* Previous arrow */}
              <Button
                variant="secondary"
                size="small"
                disabled={!pagination.canPreviousPage}
                onClick={previousPage}
                className={`w-8 h-8 p-0 flex items-center justify-center border border-ui-border-base ${
                  !pagination.canPreviousPage
                    ? "bg-ui-bg-disabled text-ui-fg-disabled cursor-not-allowed"
                    : "bg-ui-bg-base text-ui-fg-base hover:bg-ui-bg-subtle"
                }`}
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>

              {/* Page numbers */}
              {(() => {
                const pages = [];
                const maxVisiblePages = 5;
                let startPage = Math.max(
                  1,
                  pagination.currentPage - Math.floor(maxVisiblePages / 2)
                );
                let endPage = Math.min(
                  pagination.totalPages,
                  startPage + maxVisiblePages - 1
                );

                // Adjust start page if we're near the end
                if (endPage - startPage + 1 < maxVisiblePages) {
                  startPage = Math.max(1, endPage - maxVisiblePages + 1);
                }

                for (let i = startPage; i <= endPage; i++) {
                  pages.push(
                    <Button
                      key={i}
                      variant={
                        i === pagination.currentPage ? "primary" : "secondary"
                      }
                      size="small"
                      onClick={() => goToPage(i)}
                      className={`w-8 h-8 p-0 flex items-center justify-center border ${
                        i === pagination.currentPage
                          ? "bg-ui-bg-interactive text-ui-fg-on-color border-ui-bg-interactive"
                          : "bg-ui-bg-base text-ui-fg-base border-ui-border-base hover:bg-ui-bg-subtle"
                      }`}
                    >
                      {i}
                    </Button>
                  );
                }
                return pages;
              })()}

              {/* Next arrow */}
              <Button
                variant="secondary"
                size="small"
                disabled={!pagination.canNextPage}
                onClick={nextPage}
                className={`w-8 h-8 p-0 flex items-center justify-center border border-ui-border-base ${
                  !pagination.canNextPage
                    ? "bg-ui-bg-disabled text-ui-fg-disabled cursor-not-allowed"
                    : "bg-ui-bg-base text-ui-fg-base hover:bg-ui-bg-subtle"
                }`}
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>

            {/* Show per page dropdown on the right */}
            <div className="flex items-center gap-2">
              <Text className="text-sm text-ui-fg-base">Show per Page:</Text>
              <Select
                value={pagination.pageSize.toString()}
                onValueChange={(value) => changePageSize(parseInt(value))}
              >
                <Select.Trigger className="w-[60px] h-8 border-ui-border-base bg-ui-bg-base text-ui-fg-base hover:bg-ui-bg-subtle">
                  <Select.Value />
                </Select.Trigger>
                <Select.Content className="bg-ui-bg-base border-ui-border-base">
                  <Select.Item
                    value="5"
                    className="text-ui-fg-base hover:bg-ui-bg-subtle"
                  >
                    5
                  </Select.Item>
                  <Select.Item
                    value="10"
                    className="text-ui-fg-base hover:bg-ui-bg-subtle"
                  >
                    10
                  </Select.Item>
                  <Select.Item
                    value="25"
                    className="text-ui-fg-base hover:bg-ui-bg-subtle"
                  >
                    25
                  </Select.Item>
                  <Select.Item
                    value="50"
                    className="text-ui-fg-base hover:bg-ui-bg-subtle"
                  >
                    50
                  </Select.Item>
                  <Select.Item
                    value="100"
                    className="text-ui-fg-base hover:bg-ui-bg-subtle"
                  >
                    100
                  </Select.Item>
                </Select.Content>
              </Select>
            </div>
          </div>
        )}
      </Container>
    </>
  );
};

export default PageClient;
